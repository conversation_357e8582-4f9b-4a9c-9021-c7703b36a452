{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-unloadingpoint-details') }}
{% endblock %}

{% block popup %}{% endblock %}

{% block body %}
    <section class="container mx-auto px-4">
        <h1 class="font-thin text-lg mb-10 border-b-2 border-pzgreen">
            Anlagenverwaltung
        </h1>

        <div class="bg-white shadow-lg rounded-xl pb-1 mb-10 font-extralight">
            <div class="bg-gradient-to-tr from-pzpetrol-dark via-pzpetrol-light to-pzblue-light px-10 py-5 shadow-lg rounded-t-xl mb-5 font-extralight">
                <h3 class="text-pzgreen font-thin text-xl">Anlage</h3>
                <p class="text-white font-thin">{{ unloadingPoint.name1 }}</p>
            </div>

            {% block content %}
                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'unloadingPoint.field.dsdid'|trans, value: unloadingPoint.dsdId })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'unloadingPoint.field.esaid'|trans, value: unloadingPoint.unloadingPointId })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'unloadingPoint.field.name'|trans, value: unloadingPoint.name1 })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'unloadingPoint.field.state'|trans, value: unloadingPoint.state.name })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'Max. Tageskontingent', value: '20' })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'Max. Wochenkontingent', value: '100' })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'Kontingent Gültig von', value: '' })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'Kontingent Gültig bis', value: '' })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}

                <div class="mt-10 px-10">
                    <div class="flex flex-col lg:flex-row justify-between mb-5">
                        <div class="mb-5">
                            <h3 class="text-xl font-extralight mb-2">Vertragsgebiete</h3>
                            <p class="font-thin">Übersicht aller zugehörigen Vertragsgebiete der Anlage</p>
                        </div>
                    </div>
                </div>

                <div class="px-10 mb-5">
                    <div id="filters-table-unloadingPoint-contractArea"></div>
                    <table id="unloadingPoint-contractArea" class="blackgrid-contractArea theme-prezero"
                           data-source='{{ list|escape('html_attr') }}'
                           data-columns="{{ columns|json_encode|escape('html_attr') }}">
                    </table>
                </div>
            {% endblock %}

        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-unloadingpoint-details') }}
{% endblock %}

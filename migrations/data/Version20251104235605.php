<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20251104235605 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE unloading_point ADD default_daily_max_contingent INT DEFAULT NULL');
        $this->addSql('ALTER TABLE unloading_point ADD default_weekly_max_contingent INT DEFAULT NULL');
        $this->addSql('ALTER TABLE unloading_point ADD deviant_daily_max_contingent INT DEFAULT NULL');
        $this->addSql('ALTER TABLE unloading_point ADD deviant_weekly_max_contingent INT DEFAULT NULL');
        $this->addSql('ALTER TABLE unloading_point ADD deviant_contingent_valid_from TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('ALTER TABLE unloading_point ADD deviant_contingent_valid_to TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE unloading_point DROP default_daily_max_contingent');
        $this->addSql('ALTER TABLE unloading_point DROP default_weekly_max_contingent');
        $this->addSql('ALTER TABLE unloading_point DROP deviant_daily_max_contingent');
        $this->addSql('ALTER TABLE unloading_point DROP deviant_weekly_max_contingent');
        $this->addSql('ALTER TABLE unloading_point DROP deviant_contingent_valid_from');
        $this->addSql('ALTER TABLE unloading_point DROP deviant_contingent_valid_to');
    }
}
